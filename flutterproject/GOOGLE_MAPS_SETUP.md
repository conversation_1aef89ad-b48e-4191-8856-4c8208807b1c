# Google Maps 配置指南

## 🎯 当前状态
✅ Google Maps Flutter 插件已安装  
✅ 基本配置文件已设置（使用占位符 API Key）  
✅ 应用程序可以正常运行（显示地图占位符）  
⚠️ 需要配置真实的 Google Maps API Key 才能显示真实地图  

## 📋 获取 Google Maps API Key 步骤

### 1. 创建 Google Cloud 项目
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 记录项目 ID

### 2. 启用 Maps API
在 Google Cloud Console 中启用以下 API：
- **Maps JavaScript API** (用于 Web)
- **Maps SDK for Android** (用于 Android)
- **Maps SDK for iOS** (用于 iOS)

### 3. 创建 API Key
1. 转到 "APIs & Services" > "Credentials"
2. 点击 "Create Credentials" > "API Key"
3. 复制生成的 API Key

### 4. 配置 API Key 限制（推荐）
为了安全，建议限制 API Key 的使用：
- **Application restrictions**: 设置为特定的域名或应用包名
- **API restrictions**: 只允许访问 Maps 相关的 API

## 🔧 配置 API Key

### Android 配置
编辑文件：`android/app/src/main/AndroidManifest.xml`
```xml
<!-- 找到这一行并替换 YOUR_GOOGLE_MAPS_API_KEY_HERE -->
<meta-data android:name="com.google.android.geo.API_KEY"
           android:value="你的真实API_KEY"/>
```

### iOS 配置
编辑文件：`ios/Runner/Info.plist`
```xml
<!-- 找到这一行并替换 YOUR_GOOGLE_MAPS_API_KEY_HERE -->
<key>GMSApiKey</key>
<string>你的真实API_KEY</string>
```

### Web 配置
编辑文件：`web/index.html`
```html
<!-- 找到这一行并替换 YOUR_GOOGLE_MAPS_API_KEY_HERE -->
<script src="https://maps.googleapis.com/maps/api/js?key=你的真实API_KEY"></script>
```

## 🚀 启用真实 Google Maps

配置好 API Key 后，编辑 `lib/order/purchase_page/Purchase_order_process.dart`：

1. 找到 `_buildMapSection()` 方法
2. 将临时占位符容器替换为真实的 GoogleMap 组件：

```dart
// 替换临时占位符为真实 GoogleMap
ClipRRect(
  borderRadius: BorderRadius.zero,
  child: GoogleMap(
    initialCameraPosition: const CameraPosition(
      target: _shopLatLng,
      zoom: 15,
    ),
    markers: _markers,
    onMapCreated: (controller) => _mapController = controller,
    myLocationEnabled: false,
    myLocationButtonEnabled: false,
    zoomControlsEnabled: false,
    mapToolbarEnabled: false,
    compassEnabled: false,
  ),
),
```

3. 将"找我"按钮的 onTap 改回：
```dart
onTap: _goToMe,  // 替换当前的 SnackBar 显示
```

## 🎨 当前 Demo 功能

### 地图标记
- **商店标记**: 紫色圆形图标，位于左上方
- **目的地标记**: 蓝色位置图标，位于右下方

### 交互功能
- **找我按钮**: 点击后相机会平移到预设的"我的位置"坐标

### 坐标设置（澳门地区）
```dart
static const LatLng _shopLatLng = LatLng(22.198745, 113.543873); // 大三巴附近
static const LatLng _destLatLng = LatLng(22.202000, 113.551000); // 目的地
static const LatLng _myLatLng = LatLng(22.200500, 113.546000);   // 我的位置
```

## 🔄 测试步骤

1. 配置 API Key
2. 重新运行应用：`flutter run -d chrome`
3. 导航到订单追踪页面
4. 查看地图是否正常显示
5. 测试"找我"按钮功能

## 💡 进阶功能

配置完成后，可以考虑添加：
- 真实定位服务（需要 `geolocator` 插件）
- 路径规划和导航
- 自定义地图标记图标
- 地图样式定制

## 🆘 常见问题

**Q: 地图显示空白**  
A: 检查 API Key 是否正确配置，确保启用了对应平台的 Maps API

**Q: Web 版本报错**  
A: 确保 `web/index.html` 中正确加载了 Google Maps JavaScript API

**Q: 移动端无法显示**  
A: 检查 Android/iOS 的 API Key 配置，确保启用了对应的 SDK
