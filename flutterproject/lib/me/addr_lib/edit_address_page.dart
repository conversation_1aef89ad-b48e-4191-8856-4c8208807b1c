import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class EditAddressPage extends StatefulWidget {
  final String? initialAddress;
  final String? initialRoom;
  final String? initialContact;
  final String? initialPhone;
  final bool initialIsDefault;

  const EditAddressPage({
    Key? key,
    this.initialAddress,
    this.initialRoom,
    this.initialContact,
    this.initialPhone,
    this.initialIsDefault = false,
  }) : super(key: key);

  @override
  State<EditAddressPage> createState() => _EditAddressPageState();
}

class _EditAddressPageState extends State<EditAddressPage> {
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _roomController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  bool _isDefault = false;

  @override
  void initState() {
    super.initState();
    _addressController.text = widget.initialAddress ?? '凤凰家园';
    _roomController.text = widget.initialRoom ?? '9号楼803';
    _contactController.text = widget.initialContact ?? '张三';
    _phoneController.text = widget.initialPhone ?? '17654543244';
    _isDefault = widget.initialIsDefault;
  }

  @override
  void dispose() {
    _addressController.dispose();
    _roomController.dispose();
    _contactController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 12),
                  _buildInputSection(),
                ],
              ),
            ),
          ),
          _buildSaveButton(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(
          Icons.arrow_back_ios,
          color: Color(0xFF333333),
          size: 20,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: const Text(
        '编辑地址',
        style: TextStyle(
          color: Color(0xFF333333),
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        TextButton(
          onPressed: _deleteAddress,
          child: const Text(
            '删除',
            style: TextStyle(
              color: Color(0xFF333333),
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  Widget _buildInputSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildInputField(
            label: '地址',
            controller: _addressController,
            hasArrow: true,
            onTap: () {
              // TODO: 实现地址搜索功能
            },
          ),
          _buildDivider(),
          _buildInputField(
            label: '门牌号',
            controller: _roomController,
          ),
          _buildDivider(),
          _buildInputField(
            label: '联系人',
            controller: _contactController,
          ),
          _buildDivider(),
          _buildInputField(
            label: '手机号',
            controller: _phoneController,
            keyboardType: TextInputType.phone,
          ),
          _buildDivider(),
          _buildDefaultAddressOption(),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    bool hasArrow = false,
    VoidCallback? onTap,
    TextInputType? keyboardType,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            SizedBox(
              width: 60,
              child: Text(
                label,
                style: const TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
            Expanded(
              child: TextField(
                controller: controller,
                enabled: onTap == null,
                keyboardType: keyboardType,
                style: const TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                ),
              ),
            ),
            if (hasArrow)
              const Icon(
                Icons.arrow_forward_ios,
                color: Color(0xFFCCCCCC),
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultAddressOption() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          const Text(
            '默认收货地址',
            style: TextStyle(
              color: Color(0xFF999999),
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              setState(() {
                _isDefault = !_isDefault;
              });
            },
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _isDefault ? const Color(0xFFB794F6) : Colors.transparent,
                border: Border.all(
                  color: _isDefault ? const Color(0xFFB794F6) : const Color(0xFFE5E5E5),
                  width: 2,
                ),
              ),
              child: _isDefault
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 12,
                    )
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 0.5,
      margin: const EdgeInsets.only(left: 76),
      color: const Color(0xFFF0F0F0),
    );
  }

  Widget _buildSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: GestureDetector(
        onTap: _saveAddress,
        child: Container(
          width: double.infinity,
          height: 48,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFB794F6), Color(0xFF9F7AEA)],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFB794F6).withOpacity(0.3),
                offset: const Offset(0, 4),
                blurRadius: 12,
                spreadRadius: 0,
              ),
            ],
          ),
          child: const Center(
            child: Text(
              '保存',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _saveAddress() {
    // TODO: 实现保存地址功能
    print('保存地址');
    Navigator.pop(context);
  }

  void _deleteAddress() {
    // TODO: 实现删除地址功能
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: const Text('确定要删除这个地址吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // 关闭对话框
                Navigator.pop(context); // 返回地址列表
              },
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }
}