import 'package:flutter/material.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';

/// 责任说明及投诉建议页面
/// 根据设计稿实现的投诉反馈页面，包含说明文字、输入框、图片上传和提交功能
class ComplaintPage extends StatefulWidget {
  const ComplaintPage({super.key});

  @override
  State<ComplaintPage> createState() => _ComplaintPageState();
}

class _ComplaintPageState extends State<ComplaintPage> {
  // 文本输入控制器
  final TextEditingController _complaintController = TextEditingController();

  // 图片选择器
  final ImagePicker _picker = ImagePicker();

  // 选中的图片列表
  final List<XFile> _selectedImages = [];

  @override
  void dispose() {
    _complaintController.dispose();
    super.dispose();
  }

  /// 选择图片方法
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image);
        });
      }
    } catch (e) {
      // 处理选择图片错误
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择图片失败，请重试')),
      );
    }
  }

  /// 删除选中的图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// 提交投诉建议
  void _submitComplaint() {
    if (_complaintController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入您的问题或建议')),
      );
      return;
    }

    // 这里可以添加提交到服务器的逻辑
    // 目前只是显示提交成功的提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('提交成功，我们会尽快处理您的反馈'),
        backgroundColor: Colors.green,
      ),
    );

    // 清空输入内容
    _complaintController.clear();
    setState(() {
      _selectedImages.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF666666),
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '责任说明及投诉建议',
          style: TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 责任说明文字
            _buildResponsibilityText(),

            const SizedBox(height: 30),

            // 问题输入框
            _buildComplaintInput(),

            const SizedBox(height: 20),

            // 图片上传区域
            _buildImageUploadSection(),

            const SizedBox(height: 40),

            // 提交按钮
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  /// 责任说明文字
  Widget _buildResponsibilityText() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '我们致力于为用户提供优质的服务体验。如果您在使用过程中遇到任何问题或有任何建议，请通过以下方式联系我们，我们会认真对待每一条反馈并及时处理。',
        style: TextStyle(
          fontSize: 14,
          height: 1.5,
          color: Color(0xFF666666),
        ),
      ),
    );
  }

  /// 投诉输入框
  Widget _buildComplaintInput() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE0E0E0)),
      ),
      child: TextField(
        controller: _complaintController,
        maxLines: 6,
        decoration: const InputDecoration(
          hintText: '请详细描述您遇到的问题或建议...',
          hintStyle: TextStyle(color: Color(0xFF999999)),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
      ),
    );
  }

  /// 图片上传区域
  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '上传图片（可选）',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            ..._selectedImages.asMap().entries.map((entry) {
              int index = entry.key;
              XFile image = entry.value;
              return Stack(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: FileImage(File(image.path)),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Positioned(
                    top: -8,
                    right: -8,
                    child: GestureDetector(
                      onTap: () => _removeImage(index),
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }),
            if (_selectedImages.length < 3)
              GestureDetector(
                onTap: _pickImage,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFFE0E0E0),
                      style: BorderStyle.solid,
                    ),
                  ),
                  child: const Icon(
                    Icons.add,
                    color: Color(0xFF999999),
                    size: 32,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  /// 提交按钮 - 带背景颜色和圆角控制
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: _submitComplaint,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF8B5CF6), // 紫色背景
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(38), // 12px 圆角
          ),
          padding: EdgeInsets.zero,
        ),
        child: const Text(
          '提交反馈',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
