import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/* 
  订单页面
*/


class OrderPage extends StatefulWidget {
  const OrderPage({super.key});

  @override
  State<OrderPage> createState() => _OrderPageState();
}

class _OrderPageState extends State<OrderPage> {
  int selectedTabIndex = 0;
  String selectedFilter = '全部';
  
  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.white,
      statusBarIconBrightness: Brightness.dark,
    ));
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // 标题栏
            _buildTitleBar(),
            // 搜索框
            _buildSearchBar(),
            // Tab栏
            _buildTabBar(),
            // 筛选标签
            _buildFilterTags(),
            // 订单列表
            Expanded(
              child: _buildOrderList(),
            ),
          ],
        ),
      ),
      // bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }
  
  
  Widget _buildTitleBar() {
    return Container(
      height: 48,
      color: Colors.white,
      child: Stack(
        children: [
          Center(
            child: Text(
              '订单',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
          Positioned(
            right: 20,
            top: 0,
            bottom: 0,
            child: Icon(
              Icons.message_outlined,
              color: Color(0xFF9B8FDB),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSearchBar() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(20, 5, 20, 15),
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          color: Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(18),
        ),
        child: Row(
          children: [
            SizedBox(width: 15),
            Icon(Icons.search, color: Colors.grey[400], size: 20),
            SizedBox(width: 10),
            Text(
              '搜索商品名称',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      selectedTabIndex = 0;
                    });
                  },
                  child: Text(
                    '进行中的订单',
                    style: TextStyle(
                      color: selectedTabIndex == 0 ? Color(0xFF9B8FDB) : Colors.black87,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    width: 155, 
                    height: 1,
                    decoration: BoxDecoration(
                      color: selectedTabIndex == 0 ?  Color(0xFF9B8FDB) : const Color.fromARGB(109, 151, 151, 151),
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Column(
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      selectedTabIndex = 1;
                    });
                  },
                  child: Text(
                    '历史订单',
                    style: TextStyle(
                      color: selectedTabIndex == 1 ? Color(0xFF9B8FDB) : Colors.black87,
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    width: 155, 
                    height: 1, 
                    decoration: BoxDecoration(
                       color: selectedTabIndex == 1 ? Color(0xFF9B8FDB) : const Color.fromARGB(109, 151, 151, 151),
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFilterTags() {
    final filters = ['全部', '代买', '代取', '代送'];
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(0, 13, 0, 16), // 添加左右内边距
      margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: filters.map((filter) {
          final isSelected = filter == selectedFilter;
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 13),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  selectedFilter = filter;
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 13, vertical: 4),
                decoration: BoxDecoration(
                  color: isSelected ? Color(0xFF9B8FDB) : Color(0xFFE8E8E8),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  filter,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[600],
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
  
  Widget _buildOrderList() {
    return ListView(
      padding: EdgeInsets.all(15),
      children: [
        _buildOrderCard(
          title: '众觅',
          badge: '代买',
          badgeColor: Color(0xFFB8A8E8),
          time1: '10:00am',
          time2: '10:00am',
          itemValue: '100000MOP',
          staffDeposit: '10MOP',
          serviceFee: '6MOP',
          totalFee: '50MOP',
          itemInfo: '小龙虾',
          fromAddress: '某某街道',
          toAddress: '某某街道',
          hasBossBadge: true,
        ),
        _buildOrderCard(
          title: '街市名AA',
          badge: '代取',
          badgeColor: Color(0xFFB0C4DE),
          time1: '10:00am',
          time2: '10:00am',
          itemValue: '100MOP',
          staffDeposit: '10MOP',
          serviceFee: '6MOP',
          totalFee: '50MOP',
          itemInfo: '小龙虾',
          fromAddress: '某某街道',
          toAddress: '某某街道',
        ),
        _buildOrderCard(
          title: '众觅',
          badge: '代送',
          badgeColor: Color(0xFF9B8FDB),
          time1: '10:00am',
          time2: '10:00am',
          itemValue: '10MOP',
          staffDeposit: '10MOP',
          serviceFee: '6MOP',
          totalFee: '50MOP',
          itemInfo: '小龙虾',
          fromAddress: '某某街道',
          toAddress: '某某街道',
        ),
      ],
    );
  }
  
  Widget _buildOrderCard({
    required String title,
    required String badge,
    required Color badgeColor,
    required String time1,
    required String time2,
    required String itemValue,
    required String staffDeposit,
    required String serviceFee,
    required String totalFee,
    String? itemInfo,
    String? fromAddress,
    String? toAddress,
    bool hasBossBadge = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.location_on_outlined, size: 18, color: Colors.grey),
                    SizedBox(width: 5),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    if (hasBossBadge) ...[
                      SizedBox(width: 8),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'BOSS保证金',
                              style: TextStyle(
                                color: Colors.black87,
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            _buildDotIndicator(),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: badgeColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    badge,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 内容区域
          Padding(
            padding: EdgeInsets.all(12),
            child: Column(
              children: [
                // 时间轴和金额信息
                _buildTimelineSection(
                  time1: time1,
                  time2: time2,
                  itemValue: itemValue,
                  staffDeposit: staffDeposit,
                  serviceFee: serviceFee,
                  totalFee: totalFee,
                ),
                SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '物品信息',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    Spacer(),
                    Text(
                      itemInfo ?? '',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10),
                Row(
                  children: [
                    Text(
                      '起送地址:',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    Spacer(),
                    Text(
                      fromAddress ?? '',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      '送达地址:',
                      style: TextStyle(color: Colors.grey[600], fontSize: 13),
                    ),
                    Spacer(),
                    Text(
                      toAddress ?? '',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTimelineSection({
    required String time1,
    required String time2,
    required String itemValue,
    required String staffDeposit,
    required String serviceFee,
    required String totalFee,
  }) {
    return Row(
      children: [
        // 时间轴列（左侧）
        Column(
          children: [
            // 上圆点
            Icon(Icons.circle, size: 8, color: const Color.fromARGB(255, 58, 58, 58)),
            // 虚线连接
            CustomPaint(
              size: Size(1, 34), // 设置虚线高度
              painter: DashedLinePainter(),
            ),
            // 下圆点
            Icon(Icons.circle, size: 8, color: const Color.fromARGB(255, 88, 88, 88)),
          ],
        ),
        SizedBox(width: 8),
        // 内容区域（右侧）
        Expanded(
          child: Column(
            children: [
              // 第一行：时间1 + 金额信息
              Row(
                children: [
                  Text(
                    time1,
                    style: TextStyle(color: Colors.grey[600], fontSize: 13),
                  ),
                  Spacer(),
                  _buildInfoRow('物品价值', itemValue),
                  SizedBox(width: 15),
                  _buildInfoRow('staff保证金', staffDeposit),
                ],
              ),
              SizedBox(height: 8),
              // 第二行：时间2 + 金额信息
              Row(
                children: [
                  Text(
                    time2,
                    style: TextStyle(color: Colors.grey[600], fontSize: 13),
                  ),
                  Spacer(),
                  _buildInfoRow('服务费', serviceFee),
                  SizedBox(width: 15),
                  _buildInfoRow('已支付', totalFee, isBold: true),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDotIndicator() {
    return Container(
      margin: EdgeInsets.only(left: 2),
      width: 6,
      height: 6,
      decoration: BoxDecoration(
        color: Color.fromARGB(255, 39, 212, 23),
        shape: BoxShape.circle,
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value, {bool isBold = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[500],
            fontSize: 11,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: Colors.black,
            fontSize: 13,
            fontWeight: isBold ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ],
    );
  }
  
  Widget _buildBottomNavItem(IconData icon, String label, bool isSelected) {
    return GestureDetector(
      onTap: () {},
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: isSelected ? Color(0xFF9B8FDB) : Colors.grey[400],
            size: 24,
          ),
          SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? Color(0xFF9B8FDB) : Colors.grey[400],
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }
}

// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color.fromARGB(255, 136, 136, 136)
      ..strokeWidth = 1;

    const dashHeight = 3.0; // 虚线段长度
    const dashSpace = 2.0;  // 虚线间隔
    double startY = 0;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}