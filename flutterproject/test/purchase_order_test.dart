import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:zhongmi/order/purchase_page/purchase_order_create.dart';

void main() {
  group('PurchaseOrderPage Tests', () {
    testWidgets('should display all input fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PurchaseOrderPage(),
        ),
      );

      // 验证页面标题
      expect(find.text('代买'), findsOneWidget);
      
      // 验证输入字段标签
      expect(find.text('物品信息'), findsOneWidget);
      expect(find.text('指定商户'), findsOneWidget);
      expect(find.text('物品价值'), findsOneWidget);
      expect(find.text('服务费'), findsOneWidget);
      expect(find.text('Staff保证金'), findsOneWidget);
      expect(find.text('体积'), findsOneWidget);
      expect(find.text('物品重量'), findsOneWidget);
      
      // 验证支付按钮
      expect(find.text('支付并下单'), findsOneWidget);
    });

    testWidgets('should show error when required fields are empty', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PurchaseOrderPage(),
        ),
      );

      // 清空物品信息字段
      final itemInfoField = find.byType(TextField).first;
      await tester.enterText(itemInfoField, '');
      
      // 点击支付按钮
      await tester.tap(find.text('支付并下单'));
      await tester.pump();

      // 验证错误消息
      expect(find.text('请输入物品信息'), findsOneWidget);
    });

    testWidgets('should accept valid numeric input for price fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PurchaseOrderPage(),
        ),
      );

      // 查找价格输入字段并输入有效数字
      final textFields = find.byType(TextField);
      
      // 假设物品价值字段是其中一个TextField
      await tester.enterText(textFields.at(2), '1500');
      await tester.pump();

      // 验证输入被接受
      expect(find.text('1500'), findsOneWidget);
    });
  });
}
