# ============================================
# 综合项目 .gitignore 文件
# 适用于 Flutter + Laravel + SDK 文档项目
# ============================================

# ============================================
# 操作系统生成的文件
# ============================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================
# IDE 和编辑器配置文件
# ============================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA / Android Studio
.idea/
*.iws
*.iml
*.ipr
.idea_modules/
atlassian-ide-plugin.xml
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================
# Flutter 相关文件
# ============================================
# Flutter/Dart/Pub 相关
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
flutter_*.png

# Flutter 构建输出
**/build/
**/android/app/debug
**/android/app/profile
**/android/app/release

# Flutter Web
**/web/build/

# Flutter 测试覆盖率
coverage/
lcov.info

# Flutter 生成的文件
**/*.g.dart
**/*.freezed.dart
**/*.gr.dart

# ============================================
# Laravel/PHP 相关文件
# ============================================
# Laravel 环境配置文件
.env
.env.backup
.env.production
.env.local
.env.*.local

# Laravel 缓存和日志
/bootstrap/cache
/storage/*.key
/storage/app/public
/storage/framework/cache/data
/storage/framework/sessions
/storage/framework/testing
/storage/framework/views
/storage/logs
/storage/pail

# Laravel 构建输出
/public/hot
/public/storage
/public/build
/public/mix-manifest.json

# Composer 依赖
/vendor/
composer.phar
composer.lock

# PHP 相关
*.log
.phpunit.result.cache
.phpunit.cache
auth.json

# Laravel 特定
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log

# ============================================
# Node.js 相关文件
# ============================================
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# Coverage 目录
lib-cov
coverage/
*.lcov
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# TypeScript 缓存
*.tsbuildinfo

# Optional npm 缓存目录
.npm

# Optional eslint 缓存
.eslintcache

# Microbundle 缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL 历史
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity 文件
.yarn-integrity

# dotenv 环境变量文件
.env
.env.test

# parcel-bundler 缓存
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建 / 生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# Temporary 文件夹
tmp/
temp/

# ============================================
# 数据库文件
# ============================================
*.sqlite
*.sqlite3
*.db
database.sqlite

# ============================================
# 日志文件
# ============================================
*.log
logs/
log/

# ============================================
# 临时文件和缓存
# ============================================
*.tmp
*.temp
*.cache
.cache/
.temp/

# ============================================
# 安全和敏感信息
# ============================================
# 密钥文件
*.key
*.pem
*.p12
*.keystore
*.jks

# 配置文件（可能包含敏感信息）
config.local.*
local.properties
secrets.json

# ============================================
# 构建和分发文件
# ============================================
# 各种构建输出
dist/
build/
out/
target/

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# ============================================
# 项目特定忽略
# ============================================
# Flutter 项目特定
flutterproject/android/local.properties
flutterproject/ios/Flutter/flutter_export_environment.sh
flutterproject/macos/Flutter/flutter_export_environment.sh

# Laravel 项目特定
newzhongmi-backend/.phpactor.json
newzhongmi-backend/.fleet/
newzhongmi-backend/.nova/
newzhongmi-backend/.zed/

# SDK 文档中的临时文件
金灝智能科技有限公司-開發SDK/temp/
金灝智能科技有限公司-開發SDK/*.tmp
